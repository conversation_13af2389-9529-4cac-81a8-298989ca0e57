# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python lib directories (but not frontend-nextjs/lib)
**/lib/
!frontend-nextjs/lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
/.pnp
.pnp.js
.yarn/install-state.gz

# Next.js
/.next/
/out/
.next
out

# Build outputs
/build
build/
dist/
out/

# Testing
/coverage
coverage/
*.lcov
.nyc_output
.pytest_cache/
.coverage
htmlcov/

# Environment variables
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local
.env.local

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Cache directories
.cache
.parcel-cache
.npm
.eslintcache
.stylelintcache

# Logs
logs
*.log
*.pid
*.seed
*.pid.lock

# Operating System
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.pem

# IDE files
.idea/
.vscode/
!.vscode/settings.json
*.swp
*.swo
*~

# Documentation builds
docs/_build/
docs/api-reference/_build/
site/

# PWA build artifacts
sw.js
workbox-*.js
precache-manifest.*.js

# Deployment
.vercel

# Backup files
*.bak
*.backup
*.tmp

# Local storage and cache
.storage/
.local/

# Temporary folders
tmp/
temp/
